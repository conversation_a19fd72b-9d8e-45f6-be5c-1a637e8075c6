"""Main LangGraph implementation for the instruction processing agent."""

from langgraph.graph import State<PERSON>raph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode, tools_condition

from agent.state import AgentState, create_initial_state
from agent.nodes import (
    validate_input_node,
    detect_language_node,
    extract_instructions_node,
    process_instruction_node,
    format_output_node
)
from agent.tools import TOOLS


def should_continue_after_validation(state: AgentState) -> str:
    """
    Conditional edge function to determine if processing should continue after validation.

    Args:
        state: Current agent state

    Returns:
        Next node name or END
    """
    if state.get("error"):
        return "format_output"
    return "detect_language"


def should_continue_processing_instructions(state: AgentState) -> str:
    """
    Conditional edge function to determine if more instructions need processing.

    Args:
        state: Current agent state

    Returns:
        Next node name or END
    """
    if state.get("error"):
        return "format_output"

    instructions = state.get("instructions", [])
    current_index = state.get("current_instruction_index", 0)

    if current_index < len(instructions):
        return "process_instruction"
    else:
        return "format_output"


def create_agent_graph():
    """
    Create and configure the LangGraph agent.

    Returns:
        Compiled LangGraph agent
    """
    # Create the state graph
    workflow = StateGraph(AgentState)

    # Add nodes
    workflow.add_node("validate_input", validate_input_node)
    workflow.add_node("detect_language", detect_language_node)
    workflow.add_node("extract_instructions", extract_instructions_node)
    workflow.add_node("process_instruction", process_instruction_node)
    workflow.add_node("tools", ToolNode(TOOLS))
    workflow.add_node("format_output", format_output_node)

    # Set entry point
    workflow.set_entry_point("validate_input")

    # Add conditional edge after validation
    workflow.add_conditional_edges(
        "validate_input",
        should_continue_after_validation,
        {
            "detect_language": "detect_language",
            "format_output": "format_output"
        }
    )

    # Add edge from detect_language to extract_instructions
    workflow.add_edge("detect_language", "extract_instructions")

    # Add conditional edge after instruction extraction to start processing
    workflow.add_conditional_edges(
        "extract_instructions",
        should_continue_processing_instructions,
        {
            "process_instruction": "process_instruction",
            "format_output": "format_output"
        }
    )

    # Add conditional edge after processing each instruction to check for tool calls
    workflow.add_conditional_edges(
        "process_instruction",
        tools_condition,
        {
            "tools": "tools",
            "__end__": "check_continue"
        }
    )

    # Add a simple node to check if we should continue processing
    workflow.add_node("check_continue", lambda state: {})

    # Add conditional edge from check_continue to either process more or format output
    workflow.add_conditional_edges(
        "check_continue",
        should_continue_processing_instructions,
        {
            "process_instruction": "process_instruction",
            "format_output": "format_output"
        }
    )

    # Add edge from tools back to check_continue
    workflow.add_edge("tools", "check_continue")
    
    # Add edge from format_output to END
    workflow.add_edge("format_output", END)
    
    # Add memory for state persistence
    memory = MemorySaver()
    
    # Compile the graph
    app = workflow.compile(checkpointer=memory)
    
    return app


def run_agent(input_text: str, thread_id: str = "default") -> dict:
    """
    Run the agent with the given input to process instructions.

    Args:
        input_text: Text containing instructions to process
        thread_id: Thread ID for conversation tracking

    Returns:
        Agent response with processed instructions and metadata
    """
    # Create the agent graph
    app = create_agent_graph()
    
    # Create initial state
    initial_state = create_initial_state(input_text)
    
    # Configure the thread
    config = {"configurable": {"thread_id": thread_id}}
    
    try:
        # Run the agent
        result = app.invoke(initial_state, config=config)
        
        return {
            "success": True,
            "output": result.get("output", "No output generated"),
            "metadata": {
                "model_used": result.get("model_used"),
                "processing_time": result.get("processing_time"),
                "timestamp": result.get("timestamp"),
                "thread_id": thread_id
            },
            "error": result.get("error")
        }
        
    except Exception as e:
        return {
            "success": False,
            "output": None,
            "metadata": {
                "thread_id": thread_id
            },
            "error": f"Agent execution failed: {str(e)}"
        }


# Example usage
if __name__ == "__main__":
    # Test the agent with multiple instructions
    test_input = """
    Hi there! I need help with a few things:

    1. Please explain what artificial intelligence is
    2. Can you tell me the weather forecast for tomorrow?
    3. Help me write a brief email to my team about our meeting next week

    Thanks for your assistance!
    """

    result = run_agent(test_input)
    print("Agent Result:")
    print(f"Success: {result['success']}")
    print(f"Output: {result['output']}")
    print(f"Error: {result['error']}")
    print(f"Metadata: {result['metadata']}")
