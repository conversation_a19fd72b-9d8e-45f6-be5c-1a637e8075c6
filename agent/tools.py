"""Tool implementations for the LangGraph agent."""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from langchain_core.tools import tool

from config.settings import settings

logger = logging.getLogger(__name__)


@tool
def send_email(to_email: str, subject: str, body: str, from_name: Optional[str] = None) -> str:
    """
    Send an email to a recipient.

    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body content
        from_name: Optional sender name (defaults to configured email)

    Returns:
        Success or error message in German
    """
    try:
        # Validate email settings
        if not all([settings.email_smtp_server, settings.email_smtp_port,
                   settings.email_username, settings.email_password]):
            return "Email-Konfiguration ist unvollständig. Bitte überprüfen Sie die SMTP-Einstellungen."

        # Create message
        msg = MIMEMultipart()
        msg['From'] = f"{from_name or settings.email_username} <{settings.email_username}>"
        msg['To'] = to_email
        msg['Subject'] = subject

        # Add body to email
        msg.attach(MIMEText(body, 'plain', 'utf-8'))

        # Create SMTP session
        server = smtplib.SMTP(settings.email_smtp_server, settings.email_smtp_port)
        server.starttls()  # Enable security
        server.login(settings.email_username, settings.email_password)

        # Send email
        text = msg.as_string()
        server.sendmail(settings.email_username, to_email, text)
        server.quit()

        logger.info(f"Email sent successfully to {to_email}")
        return f"Email erfolgreich an {to_email} gesendet."

    except Exception as e:
        error_msg = f"Fehler beim Senden der Email: {str(e)}"
        logger.error(error_msg)
        return error_msg


# List of available tools for the agent
TOOLS = [send_email]
